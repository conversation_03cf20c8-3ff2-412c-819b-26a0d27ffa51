import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.multioutput import MultiOutputRegressor
from skl2onnx import convert_sklearn
from skl2onnx.common.data_types import FloatTensorType
import onnxruntime as rt

# 生成示例数据
# 2个特征输入，3个目标输出
np.random.seed(42)
X = np.random.rand(1000, 2) * 100  # 1000个样本，2个特征
# 创建3个相关的目标变量
y1 = X[:, 0] * 0.5 + X[:, 1] * 0.3 + np.random.randn(1000) * 5
y2 = X[:, 0] * 0.2 + X[:, 1] * 0.7 + np.random.randn(1000) * 5
y3 = X[:, 0] * 0.4 + X[:, 1] * 0.4 + np.random.randn(1000) * 5
y = np.column_stack((y1, y2, y3))  # 组合成3个输出

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建多输出回归模型
# 使用RandomForestRegressor作为基础模型
base_model = RandomForestRegressor(n_estimators=100, random_state=42)
model = MultiOutputRegressor(base_model)

# 训练模型
print("开始训练模型...")
model.fit(X_train, y_train)
print("模型训练完成!")

# 在测试集上评估模型
train_score = model.score(X_train, y_train)
test_score = model.score(X_test, y_test)
print(f"训练集得分: {train_score:.4f}")
print(f"测试集得分: {test_score:.4f}")

# 将sklearn模型转换为ONNX格式
print("正在将模型转换为ONNX格式...")

# 定义输入类型
initial_type = [('float_input', FloatTensorType([None, 2]))]  # 2个输入特征

# 转换模型
onnx_model = convert_sklearn(model, initial_types=initial_type)

# 保存ONNX模型到文件
onnx_file_path = "model.onnx"
with open(onnx_file_path, "wb") as f:
    f.write(onnx_model.SerializeToString())

print(f"ONNX模型已保存到: {onnx_file_path}")

# 验证ONNX模型
print("正在验证ONNX模型...")
# 使用ONNX Runtime加载模型
sess = rt.InferenceSession(onnx_file_path)

# 准备测试数据
input_name = sess.get_inputs()[0].name
test_input = X_test[:5].astype(np.float32)  # 取前5个测试样本

# 运行推理
onnx_predictions = sess.run(None, {input_name: test_input})[0]

# 使用原始sklearn模型进行预测
sklearn_predictions = model.predict(test_input)

# 比较结果
print("验证ONNX模型与原始sklearn模型的一致性:")
print("输入数据:")
for i in range(5):
    print(f"  样本 {i+1}: [{test_input[i][0]:.2f}, {test_input[i][1]:.2f}]")

print("\nSklearn模型预测结果:")
for i in range(5):
    print(f"  样本 {i+1}: [{sklearn_predictions[i][0]:.2f}, {sklearn_predictions[i][1]:.2f}, {sklearn_predictions[i][2]:.2f}]")

print("\nONNX模型预测结果:")
for i in range(5):
    print(f"  样本 {i+1}: [{onnx_predictions[i][0]:.2f}, {onnx_predictions[i][1]:.2f}, {onnx_predictions[i][2]:.2f}]")

# 计算差异
diff = np.abs(sklearn_predictions - onnx_predictions)
max_diff = np.max(diff)
print(f"\n最大绝对差异: {max_diff:.6f}")

if max_diff < 1e-4:
    print("✓ ONNX模型验证通过!")
else:
    print("✗ ONNX模型验证失败，差异过大!")

print("训练和转换完成!")
