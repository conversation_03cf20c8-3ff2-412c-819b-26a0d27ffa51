# Models Directory

Place your ONNX model files (.onnx) in this directory.

## Example Usage

1. Copy your model file to this directory, e.g., `model.onnx`
2. Update the `.env` file to point to your model:
   ```
   MODEL_PATH=./models/your_model.onnx
   MODEL_NAME=your_model_name
   ```
3. Start the service and the model will be automatically loaded

## Supported Model Types

This service supports any ONNX model with the following input/output types:
- Float32 tensors
- Dynamic batch sizes
- Multiple inputs/outputs

## Model Requirements

- Model file must have `.onnx` extension
- Model must be compatible with ONNX Runtime
- Input/output tensors should be float32 type for best compatibility
