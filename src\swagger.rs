use utoipa::{
    openapi::security::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SecurityScheme},
    Modify, OpenApi,
};

use crate::models::{
    InferenceRequest, InferenceResponse, ModelInfo, HealthResponse,
    ErrorResponse, LoadModelRequest, LoadModelResponse, TensorData, TensorInfo
};

#[derive(OpenApi)]
#[openapi(
    paths(
        crate::handlers::health_check,
        crate::handlers::get_model_info,
        crate::handlers::load_model,
        crate::handlers::unload_model,
        crate::handlers::inference,
    ),
    components(
        schemas(
            InferenceRequest,
            InferenceResponse,
            ModelInfo,
            HealthResponse,
            ErrorResponse,
            LoadModelRequest,
            LoadModelResponse,
            TensorData,
            TensorInfo,
        )
    ),
    tags(
        (name = "Health", description = "Health check endpoints"),
        (name = "Model", description = "Model management endpoints"),
        (name = "Inference", description = "Inference endpoints")
    ),
    info(
        title = "ONNX Service API",
        version = "1.0.0",
        description = "A RESTful API service for ONNX model inference",
        contact(
            name = "ONNX Service",
            email = "<EMAIL>"
        ),
        license(
            name = "MIT",
            url = "https://opensource.org/licenses/MIT"
        )
    ),
    servers(
        (url = "http://localhost:8080", description = "Local development server"),
        (url = "http://127.0.0.1:8080", description = "Local development server (alternative)")
    )
)]
pub struct ApiDoc;

/// 自定义OpenAPI修改器
pub struct SecurityAddon;

impl Modify for SecurityAddon {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        if let Some(components) = openapi.components.as_mut() {
            components.add_security_scheme(
                "api_key",
                SecurityScheme::ApiKey(ApiKey::Header(ApiKeyValue::new("X-API-Key"))),
            )
        }
    }
}
