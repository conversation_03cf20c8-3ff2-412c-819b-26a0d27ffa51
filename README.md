# ONNX Service

一个基于Rust的高性能ONNX模型推理API服务框架，将任何ONNX模型转换为RESTful API服务。

## 功能概述

本框架提供完整的ONNX模型推理服务解决方案，支持：
- **模型类型**: 支持所有标准ONNX格式模型（.onnx文件）
- **推理类型**: 图像分类、目标检测、自然语言处理、回归预测等
- **数据格式**: Float32张量，支持动态批次大小和多输入输出
- **部署方式**: RESTful API服务，集成Swagger UI文档

## ONNX技术栈介绍

### ONNX简介
ONNX (Open Neural Network Exchange) 是一个开放的神经网络交换格式，允许不同深度学习框架之间的模型互操作。它定义了一套通用的计算图表示，使得在PyTorch、TensorFlow、Keras等框架训练的模型可以转换为统一格式进行部署。

### ONNX、ONNX Runtime、ORT关系
- **ONNX**: 模型格式标准，定义了神经网络的结构和参数存储方式
- **ONNX Runtime**: Microsoft开发的高性能推理引擎，用于执行ONNX模型
- **ORT**: ONNX Runtime的Rust绑定库，提供Rust语言的API接口

### 版本对应关系
本项目使用：
- **ORT版本**: 2.0.0-rc.10 (Rust crate)
- **ONNX Runtime版本**: 1.22.1 (底层C++库)
- **对应关系**: ORT 2.0.0-rc.10 兼容 ONNX Runtime 1.22.1

### ONNX Runtime配置说明
由于ONNX Runtime是C++库，需要正确配置动态链接库：

1. **为什么需要配置**: Rust通过FFI调用C++库，需要在运行时找到onnxruntime.dll
2. **配置位置**: `.cargo/config.toml`文件中设置库路径
3. **环境变量**:
   - `ORT_STRATEGY=system`: 使用系统安装的ONNX Runtime
   - `ORT_LIB_LOCATION`: 指定库文件路径
4. **运行时要求**: onnxruntime.dll必须在PATH环境变量中或与可执行文件同目录

## AI应用开发流程

本框架在AI应用开发流程中的定位和作用：

```
模型训练 → ONNX转换 → API服务 → MCP协议 → AI应用平台
   ↓         ↓         ↓        ↓         ↓
PyTorch   .onnx文件  本框架   标准接口   Dify等
TensorFlow            RESTful   MCP      Claude
 Keras               Swagger   协议     ChatGPT
```

### 流程节点说明

1. **模型训练阶段**
   - 使用PyTorch、TensorFlow、Keras等框架训练深度学习模型
   - 完成模型调优和验证

2. **ONNX转换阶段**
   - 将训练好的模型转换为ONNX格式（.onnx文件）
   - ONNX作为中间格式，实现跨框架兼容

3. **API服务阶段**（本框架位置）
   - 将ONNX模型封装为RESTful API服务
   - 提供标准化的HTTP接口供外部调用
   - 处理模型加载、推理请求、结果返回

4. **MCP协议阶段**
   - Model Context Protocol，连接AI模型和应用的标准协议
   - 提供统一的接口规范，便于集成

5. **AI应用平台阶段**
   - Dify、Claude、ChatGPT等平台调用模型服务
   - 为最终用户提供智能应用体验

### 本框架的价值
- **标准化部署**: 将任何ONNX模型快速转换为可调用的API服务
- **降低门槛**: 无需深度学习专业知识即可部署和使用AI模型
- **生产就绪**: 提供完整的监控、日志、错误处理机制
- **易于集成**: 标准RESTful接口，支持任何编程语言调用

## 快速开始

### 1. 环境要求
- Rust 1.70+
- Windows x64平台
- ONNX Runtime 1.22.1 (需要手动下载配置)

### 2. ONNX Runtime配置
下载并配置ONNX Runtime：
```bash
# 1. 下载ONNX Runtime 1.22.1 for Windows x64
# 从 https://github.com/microsoft/onnxruntime/releases/tag/v1.22.1
# 下载 onnxruntime-win-x64-1.22.1.zip

# 2. 解压到本地目录，例如：
# C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\

# 3. 配置.cargo/config.toml (已包含在项目中)
# 修改其中的路径为你的实际路径
```

### 3. 环境配置
```bash
# 复制配置文件
cp .env.example .env

# 编辑.env文件
MODEL_PATH=./models/your_model.onnx
MODEL_NAME=your_model_name
SERVER_PORT=8080
```

### 4. 启动服务
```bash
cargo run
```

访问地址：
- API服务: http://服务器地址:端口
- Swagger UI: http://服务器地址:端口/swagger-ui/

## API接口详解

### 1. 健康检查
```http
GET /health
```
**功能**: 检查服务运行状态和模型加载状态
**响应**:
```json
{
  "status": "healthy",
  "version": "0.1.0",
  "model_status": "loaded"
}
```

### 2. 模型管理

#### 加载模型
```http
POST /model/load
Content-Type: application/json

{
  "model_path": "./models/your_model.onnx",
  "model_name": "my_model"
}
```
**功能**: 动态加载ONNX模型文件到内存
**参数说明**:
- `model_path`: 模型文件路径（相对或绝对路径）
- `model_name`: 自定义模型名称（可选）

#### 获取模型信息
```http
GET /model/info
```
**功能**: 获取当前加载模型的详细信息
**响应包含**:
- 模型名称和路径
- 输入张量信息（名称、数据类型、形状）
- 输出张量信息（名称、数据类型、形状）
- 加载状态

#### 卸载模型
```http
POST /model/unload
```
**功能**: 从内存中卸载当前模型，释放资源

### 3. 模型推理

```http
POST /inference
Content-Type: application/json
```

**请求格式**:
```json
{
  "inputs": {
    "input_tensor_name": {
      "dtype": "f32",
      "shape": [1, 3, 224, 224],
      "data": [0.1, 0.2, 0.3, ...]
    }
  },
  "output_names": ["output_tensor_name"]
}
```

**参数详解**:
- `inputs`: 输入数据字典，键为张量名称
  - `dtype`: 数据类型，目前支持"f32"（32位浮点数）
  - `shape`: 张量形状，例如[batch_size, channels, height, width]
  - `data`: 扁平化的数据数组，长度必须等于shape各维度乘积
- `output_names`: 期望返回的输出张量名称列表（可选，默认返回所有输出）

**响应格式**:
```json
{
  "outputs": {
    "output_tensor_name": {
      "dtype": "f32",
      "shape": [1, 1000],
      "data": [0.1, 0.2, 0.3, ...]
    }
  },
  "inference_time_ms": 15.5,
  "request_id": "uuid-string"
}
```

**响应说明**:
- `outputs`: 输出数据字典，格式与输入相同
- `inference_time_ms`: 推理耗时（毫秒）
- `request_id`: 唯一请求标识符，用于日志追踪


#### 模型推理结果的作用

模型推理结果是AI模型对输入数据的"智能判断"，就像人脑对信息的处理结果。对于后端开发者来说，理解如何使用这些结果至关重要。

##### 1. 开发角度：如何处理推理结果

**结果格式理解**：
```json
{
  "outputs": {
    "output": {
      "dtype": "f32",
      "shape": [1, 1000],
      "data": [0.001, 0.002, 0.997, ...]
    }
  },
  "inference_time_ms": 15.5,
  "request_id": "uuid-string"
}
```

**常见处理模式**：
```python
# 图像分类结果处理示例
def process_classification_result(response):
    outputs = response['outputs']['output']
    probabilities = outputs['data']  # 概率数组

    # 找到最高概率的类别
    max_index = probabilities.index(max(probabilities))
    confidence = probabilities[max_index]

    # 映射到具体类别名称
    class_names = ["猫", "狗", "鸟", ...]
    predicted_class = class_names[max_index]

    return {
        "预测类别": predicted_class,
        "置信度": f"{confidence:.2%}",
        "推理耗时": f"{response['inference_time_ms']}ms"
    }
```

**不同模型类型的结果含义**：
- **图像分类**: 每个数值代表属于某个类别的概率
- **目标检测**: 包含边界框坐标、类别概率、置信度
- **文本分类**: 情感分析、主题分类的概率分布
- **回归预测**: 连续数值预测（如价格、温度等）

##### 2. 用户使用角度：业务价值转换

**智能应用场景**：
- **电商平台**: 商品图片自动分类和标签生成
- **内容审核**: 自动识别违规图片或文本
- **客服系统**: 智能分析用户问题意图
- **医疗辅助**: 医学影像初步筛查
- **金融风控**: 交易异常检测

**用户体验提升**：
```javascript
// 前端调用示例
async function analyzeUserImage(imageFile) {
    // 1. 预处理图片数据
    const tensorData = await preprocessImage(imageFile);

    // 2. 调用推理接口
    const response = await fetch('/inference', {
        method: 'POST',
        body: JSON.stringify({
            inputs: { input: tensorData }
        })
    });

    // 3. 转换为用户友好的结果
    const result = await response.json();
    const userFriendlyResult = {
        "识别结果": "这是一只可爱的金毛犬",
        "置信度": "95%",
        "建议标签": ["宠物", "狗", "金毛", "可爱"]
    };

    return userFriendlyResult;
}
```

##### 3. MCP集成角度：标准化智能服务

**MCP协议中的作用**：
MCP (Model Context Protocol) 将推理结果标准化，使AI应用平台能够统一处理不同模型的输出。

**集成流程**：
```
用户请求 → Dify平台 → MCP协议 → 本框架API → ONNX模型 → 推理结果
    ↓                                                        ↑
用户界面 ← 智能回复 ← 结果解析 ← MCP标准格式 ← JSON响应 ← 数值数组
```

**实际应用示例**：
```yaml
# Dify工作流配置示例
- name: "图片分析节点"
  type: "http_request"
  config:
    url: "http://服务器地址:端口/inference"
    method: "POST"
    body: "{{image_tensor_data}}"

- name: "结果处理节点"
  type: "code"
  config: |
    # 将数值结果转换为自然语言
    if max(outputs) > 0.8:
        return f"我看到了{predicted_class}，置信度很高"
    else:
        return "图片内容不够清晰，请重新上传"
```

**MCP标准化的好处**：
- **统一接口**: 不同模型使用相同的调用方式
- **结果一致**: 标准化的输出格式便于处理
- **易于扩展**: 新增模型无需修改上层应用
- **降低成本**: 减少重复开发和维护工作

**实际部署效果**：
当用户在Dify中上传一张图片时，系统会：
1. 自动调用本框架的推理接口
2. 获取模型的数值预测结果
3. 通过MCP协议转换为自然语言描述
4. 在聊天界面显示"这张图片显示的是一只橘猫，置信度92%"

这样，复杂的AI模型就变成了用户可以直接交互的智能助手功能。

## 使用示例

### 图像分类模型示例
```bash
# 1. 加载ResNet模型
curl -X POST http://服务器地址:端口/model/load \
  -H "Content-Type: application/json" \
  -d '{"model_path":"./models/resnet50.onnx","model_name":"resnet50"}'

# 2. 执行推理（224x224 RGB图像）
curl -X POST http://服务器地址:端口/inference \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": {
      "input": {
        "dtype": "f32",
        "shape": [1, 3, 224, 224],
        "data": [/* 150528个浮点数，表示归一化后的像素值 */]
      }
    },
    "output_names": ["output"]
  }'
```

### 文本分类模型示例
```bash
# BERT模型推理（序列长度512）
curl -X POST http://服务器地址:端口/inference \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": {
      "input_ids": {
        "dtype": "f32",
        "shape": [1, 512],
        "data": [/* 512个token ID */]
      },
      "attention_mask": {
        "dtype": "f32",
        "shape": [1, 512],
        "data": [/* 512个注意力掩码值 */]
      }
    }
  }'
```

## 配置参数

| 环境变量           | 描述                     | 默认值              | 说明                           |
| ------------------ | ------------------------ | ------------------- | ------------------------------ |
| SERVER_HOST        | 服务器绑定地址           | 127.0.0.1           | 建议生产环境使用0.0.0.0        |
| SERVER_PORT        | 服务器端口               | 8080                | 确保端口未被占用               |
| MODEL_PATH         | 默认模型文件路径         | ./models/model.onnx | 启动时自动加载的模型           |
| MODEL_NAME         | 默认模型名称             | default_model       | 用于日志和API响应              |
| INTRA_THREADS      | ONNX Runtime内部线程数   | 4                   | 单个操作的并行度，建议=CPU核数 |
| INTER_THREADS      | ONNX Runtime操作间线程数 | 1                   | 多操作并行度，通常设为1        |
| OPTIMIZATION_LEVEL | 图优化级别               | 3                   | 0-3，越高优化越激进            |
| RUST_LOG           | 日志级别                 | info                | debug/info/warn/error          |

## 技术栈
- **Rust**: 系统编程语言，保证内存安全和高性能
- **ONNX Runtime 1.22.1**: Microsoft高性能推理引擎
- **ORT 2.0.0-rc.10**: ONNX Runtime的Rust绑定
- **Axum**: 现代异步Web框架
- **Tokio**: 异步运行时
- **Utoipa**: OpenAPI/Swagger文档生成

## 常见问题

### Q: 如何确认ONNX Runtime配置正确？
A: 启动服务检查配置，如果能正常启动说明配置正确：
```bash
cargo run
```

### Q: 推理时出现"Model not loaded"错误？
A: 确保先调用`/model/load`接口加载模型，或检查.env中的MODEL_PATH是否正确。

### Q: 如何处理不同输入形状的模型？
A: 查看`/model/info`接口返回的输入张量信息，按照实际的shape和dtype构造请求。

### Q: 支持哪些数据类型？
A: 目前主要支持float32 (f32)，这是大多数深度学习模型的标准数据类型。

### Q: 如何优化推理性能？
A: 调整ONNX Runtime配置参数：
- 增加`INTRA_THREADS`（建议等于CPU核数）
- 设置`OPTIMIZATION_LEVEL=3`启用最高优化
- 对于批量推理，增加batch_size

## 开发指南

### 构建项目
```bash
# 开发构建
cargo build

# 发布构建（优化性能）
cargo build --release
```

### 运行测试
```bash
cargo test
```

### 添加新模型支持
1. 将.onnx文件放入`models/`目录
2. 使用`/model/load`接口动态加载
3. 通过`/model/info`查看输入输出规格
4. 构造相应的推理请求

## 许可证
MIT License

