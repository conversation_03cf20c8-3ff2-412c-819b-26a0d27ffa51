use anyhow::{anyhow, Result};
use ort::{session::Session, value::Value};
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::info;

use crate::config::OnnxConfig;
use crate::models::{InferenceRequest, InferenceResponse, ModelInfo, TensorData, TensorInfo};
/// ONNX推理引擎

pub struct OnnxEngine {
    session: Option<Session>,
    model_info: Option<ModelInfo>,
    config: OnnxConfig,
}

/// 线程安全的ONNX引擎
pub type SharedOnnxEngine = Arc<RwLock<OnnxEngine>>;

impl OnnxEngine {
    /// 创建新的ONNX引擎实例
    pub fn new(config: OnnxConfig) -> Self {
        Self {
            session: None,
            model_info: None,
            config,
        }
    }

    /// 加载ONNX模型
    pub async fn load_model(
        &mut self,
        model_path: &str,
        model_name: Option<String>,
    ) -> Result<ModelInfo> {
        info!("Loading ONNX model from: {}", model_path);

        // 检查文件是否存在
        if !Path::new(model_path).exists() {
            return Err(anyhow!("Model file not found: {}", model_path));
        }

        // 使用tokio::task::spawn_blocking在后台线程中加载模型，因为ort::Session::builder是同步的
        let model_path_owned = model_path.to_string();
        let session = tokio::task::spawn_blocking(move || {
            Session::builder()
                .map_err(|e| anyhow!("Failed to create session builder: {}", e))?
                .commit_from_file(&model_path_owned)
                .map_err(|e| anyhow!("Failed to load model: {}", e))
        })
        .await
        .map_err(|e| anyhow!("Task join error: {}", e))??;

        // 获取模型的输入和输出信息
        let inputs = session
            .inputs
            .iter()
            .map(|input| {
                TensorInfo {
                    name: input.name.clone(),
                    dtype: format!("{:?}", input.input_type), // 简化的类型转换
                    shape: vec![-1], // 简化处理，实际应该从ValueType中提取形状信息
                }
            })
            .collect();

        let outputs = session
            .outputs
            .iter()
            .map(|output| {
                TensorInfo {
                    name: output.name.clone(),
                    dtype: format!("{:?}", output.output_type), // 简化的类型转换
                    shape: vec![-1], // 简化处理，实际应该从ValueType中提取形状信息
                }
            })
            .collect();

        let model_info = ModelInfo {
            name: model_name.unwrap_or_else(|| {
                Path::new(model_path)
                    .file_stem()
                    .and_then(|s| s.to_str())
                    .unwrap_or("unknown")
                    .to_string()
            }),
            path: model_path.to_string(),
            inputs,
            outputs,
            is_loaded: true,
        };

        self.session = Some(session);
        self.model_info = Some(model_info.clone());

        info!("Model loaded successfully: {}", model_info.name);
        Ok(model_info)
    }

    /// 执行推理
    pub async fn inference(&mut self, request: InferenceRequest) -> Result<InferenceResponse> {
        if self.session.is_none() {
            return Err(anyhow!("Model not loaded"));
        }

        let start_time = std::time::Instant::now();

        // 验证输入数据
        for (name, tensor_data) in &request.inputs {
            tensor_data
                .validate()
                .map_err(|e| anyhow!("Invalid input data for '{}': {}", name, e))?;
        }

        // 简化实现：直接在当前线程执行推理
        // 在实际生产环境中，可能需要更复杂的异步处理
        let session = self.session.as_mut().unwrap();

        // 创建输入值
        let mut input_values = Vec::new();
        for (name, tensor_data) in &request.inputs {
            let shape_usize: Vec<usize> = tensor_data.shape.iter().map(|&x| x as usize).collect();
            let value = Value::from_array((shape_usize, tensor_data.data.clone()))
                .map_err(|e| anyhow!("Failed to create input tensor '{}': {}", name, e))?;
            input_values.push((name.as_str(), value));
        }

        // 执行推理
        let outputs = session
            .run(input_values)
            .map_err(|e| anyhow!("Inference failed: {}", e))?;

        // 处理输出
        let mut output_map = HashMap::new();
        for (i, (output_name, output_value)) in outputs.iter().enumerate() {
            let final_output_name = if i < request.output_names.len() {
                request.output_names[i].clone()
            } else {
                output_name.to_string()
            };

            let tensor_data = Self::ort_value_to_tensor_data(&output_value)?;
            output_map.insert(final_output_name, tensor_data);
        }

        let inference_time = start_time.elapsed().as_secs_f64() * 1000.0;

        Ok(InferenceResponse {
            outputs: output_map,
            inference_time_ms: inference_time,
            request_id: uuid::Uuid::new_v4().to_string(),
        })
    }

    /// 获取模型信息
    pub fn get_model_info(&self) -> Option<&ModelInfo> {
        self.model_info.as_ref()
    }

    /// 检查模型是否已加载
    pub fn is_loaded(&self) -> bool {
        self.session.is_some()
    }

    /// 卸载模型
    pub fn unload_model(&mut self) {
        self.session = None;
        self.model_info = None;
        info!("Model unloaded");
    }

    /// 将ort::Value转换为TensorData的辅助方法
    fn ort_value_to_tensor_data(value: &Value) -> Result<TensorData> {
        // 尝试提取f32张量
        if let Ok((shape, array)) = value.try_extract_tensor::<f32>() {
            let shape_vec: Vec<i64> = shape.iter().map(|&x| x as i64).collect();
            let data: Vec<f32> = array.to_vec();
            return Ok(TensorData::new("f32".to_string(), shape_vec, data));
        }

        // 如果无法提取为f32，返回错误
        Err(anyhow!("Unsupported output tensor type"))
    }
}
