use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub model: ModelConfig,
    pub onnx: OnnxConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelConfig {
    pub path: String,
    pub name: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OnnxConfig {
    pub intra_threads: usize,
    pub inter_threads: usize,
    pub optimization_level: u8,
    pub memory_pattern_optimization: bool,
}

impl Config {
    pub fn from_env() -> Result<Self> {
        dotenv::dotenv().ok();

        let server = ServerConfig {
            host: env::var("SERVER_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("SERVER_PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()?,
        };

        let model = ModelConfig {
            path: env::var("MODEL_PATH").unwrap_or_else(|_| "./models/model.onnx".to_string()),
            name: env::var("MODEL_NAME").unwrap_or_else(|_| "default_model".to_string()),
        };

        let onnx = OnnxConfig {
            intra_threads: env::var("INTRA_THREADS")
                .unwrap_or_else(|_| "4".to_string())
                .parse()?,
            inter_threads: env::var("INTER_THREADS")
                .unwrap_or_else(|_| "1".to_string())
                .parse()?,
            optimization_level: env::var("OPTIMIZATION_LEVEL")
                .unwrap_or_else(|_| "3".to_string())
                .parse()?,
            memory_pattern_optimization: env::var("MEMORY_PATTERN_OPTIMIZATION")
                .unwrap_or_else(|_| "true".to_string())
                .parse()?,
        };

        Ok(Config {
            server,
            model,
            onnx,
        })
    }

    pub fn server_address(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }
}
